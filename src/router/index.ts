import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      const VIEWS = {
  HOME: () => import('@/views/HomeView.vue'),
  ABOUT: () => import('@/views/AboutView.vue'),
  ADMIN: () => import('@/views/AdminView.vue'),
}

component: VIEWS.HOME
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('@/views/AboutView.vue')
    },
    {
      path: '/admin',
      name: 'admin',
      component: () => import('@/views/AdminView.vue')
    }
  ],
})

export default router
