import { createRouter, createWebHistory } from 'vue-router'

const VIEWS = {
  HOME: () => import('@/views/HomeView.vue'),
  ABOUT: () => import('@/views/AboutView.vue'),
  ADMIN: () => import('@/views/AdminView.vue'),
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: VIEWS.HOME,
    },
    {
      path: '/about',
      name: 'about',
      component: VIEWS.ABOUT,
    },
    {
      path: '/admin',
      name: 'admin',
      component: VIEWS.ADMIN,
    },
  ],
})

export default router
