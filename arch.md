# 项目架构文档

## 技术栈
- **前端框架**: Vue 3
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router
- **包管理**: pnpm

## 项目结构

### 根目录
- `src/`: 源代码目录
  - `App.vue`: 主应用组件
  - `main.ts`: 应用入口文件
  - `router/`: 路由配置
    - `index.ts`: 路由定义
  - `stores/`: 状态管理
    - `counter.ts`: 计数器状态管理
- `public/`: 静态资源目录
- `vite.config.ts`: Vite 配置文件
- `package.json`: 项目依赖配置

## 核心模块

### 路由
- 使用 `vue-router` 进行路由管理，当前路由配置为空。

### 状态管理
- 使用 `Pinia` 进行状态管理，示例模块为计数器 (`counter.ts`)。

## 开发环境
- **IDE**: VSCode + Volar
- **TypeScript**: 支持 `.vue` 文件的类型检查

## 构建与部署
- 使用 `pnpm` 进行依赖管理
- 使用 `Vite` 进行快速构建

## 后续扩展建议
1. 根据业务需求扩展路由配置。
2. 添加更多状态管理模块。
3. 集成 UI 组件库（如 Element Plus 或 TDesign）。